import Foundation

class StatisticsManager {
    private let userDefaults = UserDefaults.standard
    private let completedIntervalsKey = "completedIntervals"
    
    // MARK: - Запись статистики
    
    func recordCompletedInterval(duration: TimeInterval) {
        let now = Date()
        let interval = CompletedInterval(date: now, duration: duration)
        
        var intervals = getAllStoredIntervals()
        intervals.append(interval)
        
        saveIntervals(intervals)
        
        print("📊 StatisticsManager: Записан полноценный интервал. Продолжительность: \(Int(duration/60)) мин")
    }
    
    // MARK: - Получение статистики
    
    func getStatsForToday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        return getCompletedIntervalsForDateRange(from: today, to: tomorrow)
    }
    
    func getStatsForYesterday() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!
        return getCompletedIntervalsForDateRange(from: yesterday, to: today)
    }
    
    func getStatsForCurrentWeek() -> Int {
        let calendar = Calendar.current
        let now = Date()
        
        // Получаем начало недели (понедельник)
        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday == 1) ? 6 : weekday - 2 // Воскресенье = 1, Понедельник = 2
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: calendar.startOfDay(for: now))!
        let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek)!
        
        return getCompletedIntervalsForDateRange(from: startOfWeek, to: endOfWeek)
    }
    
    func getStatsForCurrentMonth() -> Int {
        let calendar = Calendar.current
        let now = Date()
        
        let startOfMonth = calendar.dateInterval(of: .month, for: now)!.start
        let endOfMonth = calendar.dateInterval(of: .month, for: now)!.end
        
        return getCompletedIntervalsForDateRange(from: startOfMonth, to: endOfMonth)
    }
    
    func getCompletedIntervalsForDateRange(from startDate: Date, to endDate: Date) -> Int {
        let intervals = getAllStoredIntervals()
        return intervals.filter { interval in
            interval.date >= startDate && interval.date < endDate
        }.count
    }
    
    func getRecentIntervals(limit: Int = 10) -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return Array(intervals.suffix(limit).reversed()).map { ($0.date, $0.duration) }
    }

    func getAllIntervals() -> [(Date, TimeInterval)] {
        let intervals = getAllStoredIntervals()
        return intervals.map { ($0.date, $0.duration) }
    }
    
    func getTotalCompletedIntervals() -> Int {
        return getAllStoredIntervals().count
    }
    
    // MARK: - Приватные методы

    func getAllStoredIntervals() -> [CompletedInterval] {
        guard let data = userDefaults.data(forKey: completedIntervalsKey) else {
            return []
        }
        
        do {
            let intervals = try JSONDecoder().decode([CompletedInterval].self, from: data)
            return intervals.sorted { $0.date < $1.date }
        } catch {
            print("❌ StatisticsManager: Ошибка декодирования интервалов: \(error)")
            return []
        }
    }
    
    func saveIntervals(_ intervals: [CompletedInterval]) {
        do {
            let data = try JSONEncoder().encode(intervals)
            userDefaults.set(data, forKey: completedIntervalsKey)
        } catch {
            print("❌ StatisticsManager: Ошибка сохранения интервалов: \(error)")
        }
    }

    // MARK: - Очистка данных

    func clearAllIntervals() {
        userDefaults.removeObject(forKey: completedIntervalsKey)
        print("📊 Все интервалы очищены из UserDefaults")
    }

    // MARK: - Тестовые данные

    func addTestData() {
        let calendar = Calendar.current
        let now = Date()

        // Добавляем интервалы за последние несколько дней
        for dayOffset in 1...7 {
            guard let workDay = calendar.date(byAdding: .day, value: -dayOffset, to: now) else { continue }

            // Случайное количество интервалов в день (2-6)
            let intervalsPerDay = Int.random(in: 2...6)

            for intervalIndex in 0..<intervalsPerDay {
                // Начинаем работу в 9 утра + случайное время
                let startHour = 9 + Int.random(in: 0...8)
                let startMinute = intervalIndex * 60 + Int.random(in: 0...30)

                if let intervalTime = calendar.date(bySettingHour: startHour, minute: startMinute, second: 0, of: workDay) {
                    // Создаем интервал с нужной датой
                    let interval = CompletedInterval(date: intervalTime, duration: 3120) // 52 минуты
                    var intervals = getAllStoredIntervals()
                    intervals.append(interval)
                    saveIntervals(intervals)
                }
            }
        }

        print("📊 Добавлены тестовые данные для демонстрации")
    }
}

// MARK: - Модель данных

struct CompletedInterval: Codable {
    let date: Date
    let duration: TimeInterval
    
    init(date: Date, duration: TimeInterval) {
        self.date = date
        self.duration = duration
    }
}
