import Cocoa

class SettingsWindow: NSWindow {

    private var intervalDurationField: NSTextField!
    private var testModeCheckbox: NSButton!
    private var onSettingsChanged: ((TimeInterval) -> Void)?
    
    init() {
        // Создаем окно настроек
        let windowRect = NSRect(x: 0, y: 0, width: 400, height: 250)
        
        super.init(
            contentRect: windowRect,
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )
        
        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        self.title = "Настройки Pomodoro"
        self.isReleasedWhenClosed = false
        self.center()
        
        // Делаем окно всегда поверх других
        self.level = .floating
    }
    
    private func setupUI() {
        let contentView = NSView(frame: self.contentRect(forFrameRect: self.frame))
        self.contentView = contentView
        
        // Заголовок
        let titleLabel = NSTextField(labelWithString: "Настройки таймера")
        titleLabel.font = NSFont.systemFont(ofSize: 16, weight: .bold)
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(titleLabel)
        
        // Чекбокс для тестового режима
        testModeCheckbox = NSButton(checkboxWithTitle: "Режим тестирования (секунды)", target: self, action: #selector(testModeChanged))
        testModeCheckbox.font = NSFont.systemFont(ofSize: 13)
        testModeCheckbox.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(testModeCheckbox)

        // Лейбл для поля ввода
        let intervalLabel = NSTextField(labelWithString: "Длительность интервала:")
        intervalLabel.font = NSFont.systemFont(ofSize: 13)
        intervalLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(intervalLabel)

        // Поле ввода для длительности интервала
        intervalDurationField = NSTextField()
        updateFieldValue()
        intervalDurationField.placeholderString = "Введите значение"
        intervalDurationField.translatesAutoresizingMaskIntoConstraints = false
        intervalDurationField.target = self
        intervalDurationField.action = #selector(intervalDurationChanged)
        contentView.addSubview(intervalDurationField)
        
        // Описание
        let descriptionLabel = NSTextField(labelWithString: "В режиме тестирования автоматически устанавливается 3 секунды")
        descriptionLabel.font = NSFont.systemFont(ofSize: 11)
        descriptionLabel.textColor = NSColor.secondaryLabelColor
        descriptionLabel.alignment = .center
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(descriptionLabel)
        
        // Кнопки
        let buttonStackView = NSStackView()
        buttonStackView.orientation = .horizontal
        buttonStackView.spacing = 10
        buttonStackView.translatesAutoresizingMaskIntoConstraints = false
        
        let saveButton = NSButton(title: "Сохранить", target: self, action: #selector(saveButtonClicked))
        saveButton.bezelStyle = .rounded
        saveButton.keyEquivalent = "\r" // Enter
        
        let cancelButton = NSButton(title: "Отмена", target: self, action: #selector(cancelButtonClicked))
        cancelButton.bezelStyle = .rounded
        cancelButton.keyEquivalent = "\u{1b}" // Escape
        
        buttonStackView.addArrangedSubview(cancelButton)
        buttonStackView.addArrangedSubview(saveButton)
        contentView.addSubview(buttonStackView)
        
        // Constraints
        NSLayoutConstraint.activate([
            // Title
            titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Test mode checkbox
            testModeCheckbox.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 20),
            testModeCheckbox.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            testModeCheckbox.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Interval label
            intervalLabel.topAnchor.constraint(equalTo: testModeCheckbox.bottomAnchor, constant: 15),
            intervalLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            intervalLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Interval field
            intervalDurationField.topAnchor.constraint(equalTo: intervalLabel.bottomAnchor, constant: 8),
            intervalDurationField.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            intervalDurationField.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            intervalDurationField.heightAnchor.constraint(equalToConstant: 24),

            // Description
            descriptionLabel.topAnchor.constraint(equalTo: intervalDurationField.bottomAnchor, constant: 10),
            descriptionLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            descriptionLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Buttons
            buttonStackView.topAnchor.constraint(equalTo: descriptionLabel.bottomAnchor, constant: 30),
            buttonStackView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            buttonStackView.bottomAnchor.constraint(lessThanOrEqualTo: contentView.bottomAnchor, constant: -20)
        ])
    }
    
    func setOnSettingsChanged(_ callback: @escaping (TimeInterval) -> Void) {
        self.onSettingsChanged = callback
    }
    
    func updateIntervalDuration(_ duration: TimeInterval) {
        updateFieldValue()
    }

    private func updateFieldValue() {
        if testModeCheckbox.state == .on {
            // Режим тестирования - показываем секунды
            intervalDurationField.stringValue = String(Int(PomodoroTimer.workDuration))
        } else {
            // Обычный режим - показываем минуты
            intervalDurationField.stringValue = String(Int(PomodoroTimer.workDuration / 60))
        }
    }

    @objc private func testModeChanged() {
        if testModeCheckbox.state == .on {
            // Автоматически устанавливаем 3 секунды при включении тестового режима
            intervalDurationField.stringValue = "3"
            intervalDurationField.placeholderString = "Введите количество секунд"
        } else {
            // Возвращаем к минутам
            intervalDurationField.stringValue = String(Int(PomodoroTimer.workDuration / 60))
            intervalDurationField.placeholderString = "Введите количество минут"
        }

        // Обновляем цвет текста
        intervalDurationField.textColor = NSColor.labelColor
    }
    
    @objc private func intervalDurationChanged() {
        // Валидация ввода в реальном времени
        let text = intervalDurationField.stringValue
        if let value = Int(text), value > 0 {
            intervalDurationField.textColor = NSColor.labelColor
        } else {
            intervalDurationField.textColor = NSColor.systemRed
        }
    }
    
    @objc private func saveButtonClicked() {
        let text = intervalDurationField.stringValue.trimmingCharacters(in: .whitespacesAndNewlines)

        guard let value = Int(text), value > 0 else {
            let unit = testModeCheckbox.state == .on ? "секунд" : "минут"
            showAlert(title: "Ошибка", message: "Пожалуйста, введите корректное количество \(unit) (больше 0)")
            return
        }

        let duration: TimeInterval
        if testModeCheckbox.state == .on {
            // Режим тестирования - значение в секундах
            duration = TimeInterval(value)
        } else {
            // Обычный режим - значение в минутах
            duration = TimeInterval(value * 60)
        }

        onSettingsChanged?(duration)
        close()
    }
    
    @objc private func cancelButtonClicked() {
        close()
    }
    
    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.alertStyle = .warning
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    func showWindow() {
        self.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
}
