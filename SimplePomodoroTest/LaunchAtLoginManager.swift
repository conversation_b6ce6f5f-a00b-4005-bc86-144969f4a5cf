import Cocoa
import ServiceManagement

class LaunchAtLoginManager {
    static let shared = LaunchAtLoginManager()
    
    private let bundleIdentifier = Bundle.main.bundleIdentifier ?? "com.local.uProd"
    private let launchAtLoginKey = "LaunchAtLogin"
    
    private init() {}
    
    /// Проверяет, включен ли автозапуск
    var isEnabled: Bool {
        get {
            // Сначала проверяем настройки пользователя
            let userDefaults = UserDefaults.standard
            if userDefaults.object(forKey: launchAtLoginKey) == nil {
                // Если настройка не установлена, включаем по умолчанию
                userDefaults.set(true, forKey: launchAtLoginKey)
                return true
            }
            return userDefaults.bool(forKey: launchAtLoginKey)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: launchAtLoginKey)
            updateLaunchAtLogin(enabled: newValue)
        }
    }
    
    /// Обновляет статус автозапуска в системе
    private func updateLaunchAtLogin(enabled: Bool) {
        if #available(macOS 13.0, *) {
            // Используем новый API для macOS 13+
            updateLaunchAtLoginModern(enabled: enabled)
        } else {
            // Используем старый API для более старых версий
            updateLaunchAtLoginLegacy(enabled: enabled)
        }
    }
    
    /// Современный способ управления автозапуском (macOS 13+)
    @available(macOS 13.0, *)
    private func updateLaunchAtLoginModern(enabled: Bool) {
        do {
            let service = SMAppService.mainApp
            if enabled {
                if service.status == .notRegistered {
                    try service.register()
                    print("🚀 uProd: Launch at login enabled (modern)")
                }
            } else {
                if service.status == .enabled {
                    try service.unregister()
                    print("🚀 uProd: Launch at login disabled (modern)")
                }
            }
        } catch {
            print("🚀 uProd: Failed to update launch at login: \(error)")
        }
    }
    
    /// Устаревший способ управления автозапуском (macOS 12 и ниже)
    private func updateLaunchAtLoginLegacy(enabled: Bool) {
        let success = SMLoginItemSetEnabled(bundleIdentifier as CFString, enabled)
        if success {
            print("🚀 uProd: Launch at login \(enabled ? "enabled" : "disabled") (legacy)")
        } else {
            print("🚀 uProd: Failed to update launch at login (legacy)")
        }
    }
    
    /// Инициализирует автозапуск при первом запуске приложения
    func initializeOnFirstLaunch() {
        let firstLaunchKey = "HasLaunchedBefore"
        let userDefaults = UserDefaults.standard
        
        if !userDefaults.bool(forKey: firstLaunchKey) {
            // Первый запуск - включаем автозапуск по умолчанию
            print("🚀 uProd: First launch detected, enabling launch at login by default")
            userDefaults.set(true, forKey: firstLaunchKey)
            userDefaults.set(true, forKey: launchAtLoginKey)
            updateLaunchAtLogin(enabled: true)
        } else {
            // Не первый запуск - синхронизируем с сохраненными настройками
            let shouldLaunchAtLogin = userDefaults.bool(forKey: launchAtLoginKey)
            updateLaunchAtLogin(enabled: shouldLaunchAtLogin)
        }
    }
    
    /// Проверяет текущий статус в системе (для отладки)
    func checkSystemStatus() -> String {
        if #available(macOS 13.0, *) {
            let service = SMAppService.mainApp
            switch service.status {
            case .notRegistered:
                return "Not registered"
            case .enabled:
                return "Enabled"
            case .requiresApproval:
                return "Requires approval"
            case .notFound:
                return "Not found"
            @unknown default:
                return "Unknown"
            }
        } else {
            // Для старых версий сложнее проверить статус
            return "Legacy mode (status unknown)"
        }
    }
}
