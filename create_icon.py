#!/usr/bin/env python3
"""
Создает иконку для uProd - современную, минималистичную иконку для приложения продуктивности
"""

import os
from PIL import Image, ImageDraw, ImageFont
import math

def create_uprod_icon(size):
    """Создает иконку uProd заданного размера"""

    # Создаем изображение с прозрачным фоном
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # Цвета для современного дизайна
    primary_color = (74, 144, 226)    # Синий
    secondary_color = (155, 89, 182)  # Фиолетовый
    accent_color = (46, 204, 113)     # Зеленый
    white_color = (255, 255, 255)     # Белый

    # Размеры элементов относительно размера иконки
    margin = size * 0.1
    circle_radius = (size - 2 * margin) / 2
    center = size / 2

    # Рисуем основной круг с градиентом
    draw.ellipse([
        margin, margin,
        size - margin, size - margin
    ], fill=primary_color)

    # Рисуем внутренний круг для глубины
    inner_margin = margin + size * 0.05
    draw.ellipse([
        inner_margin, inner_margin,
        size - inner_margin, size - inner_margin
    ], fill=secondary_color)

    # Создаем символ "u" из геометрических фигур
    # Рисуем букву "u" как две вертикальные линии и дугу
    line_width = max(2, size // 16)
    u_width = size * 0.3
    u_height = size * 0.35

    # Левая вертикальная линия
    left_x = center - u_width / 2
    top_y = center - u_height / 2
    bottom_y = center + u_height / 4

    draw.rectangle([
        left_x - line_width/2, top_y,
        left_x + line_width/2, bottom_y
    ], fill=white_color)

    # Правая вертикальная линия
    right_x = center + u_width / 2
    draw.rectangle([
        right_x - line_width/2, top_y,
        right_x + line_width/2, bottom_y
    ], fill=white_color)

    # Нижняя дуга (полукруг)
    arc_radius = u_width / 2
    arc_center_y = bottom_y
    draw.ellipse([
        center - arc_radius, arc_center_y - line_width,
        center + arc_radius, arc_center_y + arc_radius
    ], fill=white_color)

    # Убираем верхнюю часть дуги, чтобы получилась буква "u"
    draw.rectangle([
        center - arc_radius, arc_center_y - line_width,
        center + arc_radius, arc_center_y + line_width
    ], fill=secondary_color)

    # Добавляем акцентную точку для стиля
    dot_size = size * 0.06
    dot_x = center + circle_radius * 0.5
    dot_y = center - circle_radius * 0.5

    draw.ellipse([
        dot_x - dot_size, dot_y - dot_size,
        dot_x + dot_size, dot_y + dot_size
    ], fill=accent_color)

    return img

def main():
    """Создает все необходимые размеры иконок"""
    
    # Размеры иконок для macOS
    sizes = [16, 32, 64, 128, 256, 512, 1024]
    
    # Создаем папку для иконок
    icon_dir = "SimplePomodoroTest/Assets.xcassets/AppIcon.appiconset"
    
    print("🎨 Создаем иконки uProd...")
    
    for size in sizes:
        print(f"  📐 Создаем иконку {size}x{size}...")
        
        # Создаем иконку
        icon = create_uprod_icon(size)
        
        # Сохраняем в разных форматах для разных размеров
        if size <= 32:
            # Для маленьких размеров сохраняем как 1x и 2x
            icon.save(f"{icon_dir}/icon_{size}x{size}.png", "PNG")
            if size == 16:
                icon.save(f"{icon_dir}/<EMAIL>", "PNG")
            elif size == 32:
                icon.save(f"{icon_dir}/<EMAIL>", "PNG")
        else:
            # Для больших размеров
            icon.save(f"{icon_dir}/icon_{size}x{size}.png", "PNG")
            if size == 256:
                icon.save(f"{icon_dir}/<EMAIL>", "PNG")
            elif size == 512:
                icon.save(f"{icon_dir}/<EMAIL>", "PNG")
            elif size == 1024:
                icon.save(f"{icon_dir}/<EMAIL>", "PNG")
    
    print("✅ Иконки созданы!")
    print(f"📁 Файлы сохранены в: {icon_dir}")

if __name__ == "__main__":
    main()
